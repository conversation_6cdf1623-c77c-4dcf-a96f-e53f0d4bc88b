// React core
// Third-party library imports
// Services
// Components
import { SimpleVoiceChat } from "../../SimpleVoiceChat";
// Utils & Constants & Helpers
// Styles

interface GameProps {
  generatedCharacter: string;
  gameStarted: boolean;
  isGameStarted: boolean;
  initialMessage: string;
  onGameEnd: (gameWon: boolean) => void;
  onShowGameLive: () => void;
  onShowGameHint: () => void;
  onShowGameExit: () => void;
}

export const Game: React.FC<GameProps> = ({
  generatedCharacter,
  gameStarted,
  isGameStarted,
  initialMessage,
  onGameEnd,
  onShowGameLive,
  onShowGameHint,
  onShowGameExit,
}) => {
  return (
    <div className="view-main-menu">
      <div className="container">
        <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

        <SimpleVoiceChat
          generatedCharacter={generatedCharacter}
          isGameStarted={isGameStarted}
          initialMessage={initialMessage}
          onGameEnd={onGameEnd}
        />

        {(generatedCharacter || gameStarted) && (
          <>
            <div className="game-navigation">
              <button onClick={onShowGameLive} className="nav-button">
                ❤️ Vidas
              </button>

              <button onClick={onShowGameHint} className="nav-button">
                💡 Pistas
              </button>

              <button onClick={onShowGameExit} className="nav-button">
                🚪 Salir
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
