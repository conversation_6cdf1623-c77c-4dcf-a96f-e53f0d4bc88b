import { useEffect, useRef, useState } from "react";
import { useRealTimeConversation } from "../hooks/useRealTimeConversation";
import { AnimatedVoiceButton } from "./AnimatedVoiceButton";
import { ChatArea } from "./ChatArea";
import { ConversationStorage } from "../services/ConversationStorage";
import type { GameProgress as GameProgressType } from "../services/ConversationStorage";

interface SimpleVoiceChatProps {
  generatedCharacter?: string;
  isGameStarted: boolean;
  initialMessage?: string;
  onGameEnd?: (gameWon: boolean) => void;
}

export const SimpleVoiceChat: React.FC<SimpleVoiceChatProps> = ({
  generatedCharacter,
  isGameStarted,
  initialMessage,
  onGameEnd,
}) => {
  // Refs and state for game management
  const autoStartAttempted = useRef(false);
  const gameEndTriggered = useRef(false);
  const [gameProgress, setGameProgress] = useState<GameProgressType | null>(
    null
  );
  const [showGameEndScreen, setShowGameEndScreen] = useState(false);
  const conversationStorage = useRef(ConversationStorage.getInstance());

  // Hook for real-time conversation management
  const {
    isActive,
    conversationState,
    messages,
    isSupported,
    error,
    startConversation,
    stopConversation,
    enableSmartMicrophone,
    addInitialMessage,
  } = useRealTimeConversation(
    generatedCharacter,
    isGameStarted,
    gameProgress?.gameFinished
  );

  /**
   * Add initial message when it arrives
   */
  useEffect(() => {
    if (initialMessage && isGameStarted) {
      addInitialMessage(initialMessage);
    }
  }, [initialMessage, isGameStarted, addInitialMessage]);

  /**
   * Update game progress when session changes
   */
  useEffect(() => {
    if (isGameStarted) {
      const updateProgress = () => {
        const progress = conversationStorage.current.getGameProgress();
        setGameProgress(progress);

        // Notify parent when game finishes (only once)
        if (progress?.gameFinished && !gameEndTriggered.current && onGameEnd) {
          console.log("🎮 Game finished detected:", {
            gameFinished: progress.gameFinished,
            gameWon: progress.gameWon,
          });
          gameEndTriggered.current = true;
          setShowGameEndScreen(true);
          onGameEnd(progress.gameWon);
        }
      };

      updateProgress();
      const interval = setInterval(updateProgress, 1000);
      return () => clearInterval(interval);
    }
  }, [isGameStarted, messages, onGameEnd]);

  useEffect(() => {

    // Auto-start evaluation when state becomes idle
    if (
      conversationState === "idle" &&
      isGameStarted &&
      !isActive &&
      isSupported &&
      !error &&
      initialMessage &&
      !autoStartAttempted.current
    ) {
      // console.log("🎤 Estado cambió a idle, verificando auto-start...");
    }
  }, [
    conversationState,
    isGameStarted,
    isActive,
    isSupported,
    error,
    initialMessage,
  ]);

  /**
   * Reset flags when game restarts
   */
  useEffect(() => {
    if (!isGameStarted) {
      autoStartAttempted.current = false;
      gameEndTriggered.current = false;
    }
  }, [isGameStarted]);

  /**
   * Stop conversation when game finishes
   */
  useEffect(() => {
    if (gameProgress?.gameFinished && isActive) {
      console.log("🔇 Game finished, stopping conversation");
      stopConversation();
    }
  }, [gameProgress?.gameFinished, isActive, stopConversation]);

  /**
   * Handle voice button click - start/stop conversation
   */
  const handleButtonClick = () => {
    // Don't allow microphone activation if game has finished
    if (gameProgress?.gameFinished) {
      console.log("🔇 Game has finished, microphone disabled");
      return;
    }

    if (isActive) {
      stopConversation();
    } else {
      startConversation().then((success) => {
        if (success) {
          enableSmartMicrophone();
        }
      });
    }
  };

  if (!isGameStarted) {
    return null;
  }

  if (!isSupported) {
    return (
      <div
        style={{
          backgroundColor: "#fff3cd",
          border: "2px solid #ffc107",
          borderRadius: "12px",
          padding: "20px",
          marginTop: "20px",
          textAlign: "center",
        }}
      >
        <h4 style={{ color: "#856404", marginBottom: "8px" }}>
          ⚠️ Reconocimiento de voz no disponible
        </h4>
        <p style={{ color: "#856404", margin: 0 }}>
          Tu navegador no soporta esta funcionalidad
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="voice-chat-container">
        <div
          className="character-title"
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            gap: "12px",
          }}
        >
          <span>{generatedCharacter}</span>
        </div>

        {/* Animated Voice Button */}
        <div className="voice-button-container">
          <AnimatedVoiceButton
            state={gameProgress?.gameFinished ? "disabled" : conversationState}
            isActive={isActive && !gameProgress?.gameFinished}
            onClick={handleButtonClick}
            disabled={gameProgress?.gameFinished}
          />
        </div>

        {/* Error Message */}
        {error && <div className="error-message">❌ {error}</div>}

        {/* Chat Area */}
        <ChatArea
          messages={messages}
          isActive={isActive}
          isGameStarted={isGameStarted}
          initialMessage={initialMessage}
        />

        {/* Status Indicator */}
        {/* <div className="status-indicator">
          {gameProgress?.gameFinished
            ? "🎯 Juego finalizado • El micrófono está deshabilitado"
            : "💡 Habla cuando veas el micrófono pulsando • El sistema gestiona todo automáticamente"}
        </div> */}

        {/* Debug Information */}
        {/* <div className="debug-info">
          Debug: isActive={isActive.toString()}, state={conversationState},
          attempted={autoStartAttempted.current.toString()}
        </div> */}
      </div>

      {/* Hints Popup */}
      {/* <HintsPopup
        hints={gameProgress?.hints || []}
        isOpen={showHintsPopup}
        onClose={handleCloseHints}
      /> */}

      {/* Character Info Modal */}
      {/* <CharacterInfo
        characterName={generatedCharacter || ""}
        isVisible={showCharacterInfo}
        onClose={handleCloseCharacterInfo}
      /> */}

      {/* Movistar Info Modal */}
      {/* <MovistarInfo
        characterName={generatedCharacter || ""}
        isVisible={showMovistarInfo}
        onClose={handleCloseMovistarInfo}
      /> */}

      {/* Game End Screen - Now handled by parent App component */}
      {/* {showGameEndScreen && gameProgress && (
        <GameEndScreen
          gameWon={gameProgress.gameWon}
          onClose={handleCloseGameEndScreen}
          onNewGame={handleNewGame}
        />
      )} */}
    </>
  );
};
